"use client";

import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { usePathname } from 'next/navigation';
import { clientFetch } from '@/lib/clientFetch';
import type { UserMe } from '@/lib/types';
import { useEffect, useState } from 'react';

export default function Header() {
  const pathname = usePathname();
  const isAuthPage = pathname?.includes('/login') || pathname?.includes('/auth');
  const [hasToken, setHasToken] = useState(false);

  // Check if we have an auth token in cookies
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const cookies = document.cookie;
      const hasAccessToken = cookies.includes('access_token=') && !cookies.includes('access_token=;');
      setHasToken(hasAccessToken);
    }
  }, []);

  const { data: me, error } = useQuery<UserMe>({
    queryKey: ['me'],
    queryFn: () => clientFetch<UserMe>('/api/auth/me'),
    staleTime: 60_000,
    enabled: !isAuthPage && hasToken, // Only run if not on auth page AND we have a token
    retry: false, // Don't retry auth queries
  });

  const onLogout = async () => {
    await clientFetch('/api/auth/logout', { method: 'POST' });
    window.location.href = '/login';
  };

  return (
    <header className="w-full border-b bg-white">
      <div className="mx-auto max-w-6xl px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-6">
          <Link href="/dashboard" className="font-semibold">ABA Portal</Link>
          {me && (
            <nav className="hidden md:flex items-center gap-4 text-sm text-gray-700">
              <Link href="/clients" className="hover:underline">Clients</Link>
              <Link href="/sessions" className="hover:underline">Sessions</Link>
            </nav>
          )}
        </div>
        <div className="flex items-center gap-3">
          {me ? (
            <>
              <span className="text-sm text-gray-700">{me.full_name || me.email} · {me.role}</span>
              <button onClick={onLogout} className="text-sm text-gray-600 hover:text-gray-900 underline">Logout</button>
            </>
          ) : (
            <Link href="/login" className="text-sm text-gray-600 hover:text-gray-900 underline">Login</Link>
          )}
        </div>
      </div>
    </header>
  );
}
