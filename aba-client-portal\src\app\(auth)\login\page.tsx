import { Suspense } from 'react';
import AuthForm from '@/components/AuthForm';

export default function LoginPage() {
  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="w-full max-w-md p-6 bg-white rounded-md shadow-sm">
        <h1 className="text-xl font-semibold mb-4">Sign in</h1>
        <Suspense fallback={<div>Loading…</div>}>
          <AuthForm />
        </Suspense>
      </div>
    </div>
  );
}
