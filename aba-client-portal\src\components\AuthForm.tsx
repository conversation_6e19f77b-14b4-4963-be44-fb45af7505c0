"use client";

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { loginSchema, type LoginSchema } from '@/lib/validators';
import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { clientFetch } from '@/lib/clientFetch';
import type { TokenResponse } from '@/lib/types';

export default function AuthForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const message = searchParams.get('message');
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginSchema>({ resolver: zodResolver(loginSchema) });

  const onSubmit = async (data: LoginSchema) => {
    setError(null);
    try {
      await clientFetch<TokenResponse>('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify(data),
      });
      router.push('/dashboard');
    } catch (e) {
      const msg = (e as Error)?.message || '';
      if (msg.toLowerCase().includes('401') || msg.toLowerCase().includes('unauthorized')) {
        setError('Invalid email or password');
      } else {
        setError('Login failed');
      }
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 w-full max-w-sm">
      {!!message && <p className="text-sm text-amber-700 bg-amber-50 p-2 rounded">{message}</p>}
      {!!error && <p className="text-sm text-red-700 bg-red-50 p-2 rounded" role="alert">{error}</p>}

      <div>
        <label className="block text-sm font-medium">Email</label>
        <input
          type="email"
          autoComplete="email"
          {...register('email')}
          className="mt-1 w-full rounded border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.email && <p className="text-xs text-red-600 mt-1">{errors.email.message}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium">Password</label>
        <input
          type="password"
          autoComplete="current-password"
          {...register('password')}
          className="mt-1 w-full rounded border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.password && <p className="text-xs text-red-600 mt-1">{errors.password.message}</p>}
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full rounded bg-blue-600 text-white py-2 hover:bg-blue-700 disabled:opacity-50"
      >
        {isSubmitting ? 'Signing in…' : 'Sign in'}
      </button>

      <div className="text-xs text-gray-500">
        Demo: <EMAIL> / therapistpass · <EMAIL> / parentpass
      </div>
    </form>
  );
}
