import { z } from 'zod';

export const loginSchema = z.object({
  email: z.string().email('Enter a valid email'),
  password: z.string().min(1, 'Password is required'),
});

export type LoginSchema = z.infer<typeof loginSchema>;

export const sessionCreateSchema = z.object({
  client_id: z.string().min(1, 'Client is required'),
  date: z.string().datetime().optional(),
  notes: z.string().optional(),
  goals: z.array(z.string().min(1)).optional(),
  metrics: z
    .record(z.string(), z.number())
    .optional(),
});

export type SessionCreateSchema = z.infer<typeof sessionCreateSchema>;
