"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';
import type { ProgressChartResponse } from '@/lib/types';

export default function ProgressChart({ data }: { data: ProgressChartResponse }) {
  const { dates, series } = data;

  const colors = [
    '#3b82f6', '#10b981', '#ef4444', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16', '#f472b6', '#6366f1', '#14b8a6',
  ];

  const lines = Object.keys(series);
  const chartData = dates.map((date, idx) => {
    const row: Record<string, number | string> = { date };
    for (const key of lines) {
      row[key] = series[key][idx] ?? 0;
    }
    return row;
  });

  return (
    <div className="w-full h-80">
      <ResponsiveContainer>
        <LineChart data={chartData} margin={{ top: 10, right: 30, left: 10, bottom: 10 }}>
          <XAxis dataKey="date" tick={{ fontSize: 12 }} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip />
          <Legend />
          {lines.map((key, idx) => (
            <Line key={key} type="monotone" dataKey={key} stroke={colors[idx % colors.length]} dot={false} />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
