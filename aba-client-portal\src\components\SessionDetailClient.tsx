"use client";

import { useQuery } from '@tanstack/react-query';
import { clientFetch } from '@/lib/clientFetch';
import type { SessionOut } from '@/lib/types';

export default function SessionDetailClient({ sessionId }: { sessionId: string }) {
  const { data: session } = useQuery<SessionOut>({ queryKey: ['session', sessionId], queryFn: () => clientFetch<SessionOut>(`/api/sessions/${sessionId}`) });

  if (!session) return <p>Loading…</p>;

  return (
    <div className="space-y-4">
      <h1 className="text-xl font-semibold">Session Details</h1>
      <div className="rounded border p-4 space-y-2">
        <p><span className="font-medium">Date:</span> {new Date(session.date).toLocaleString()}</p>
        {session.notes && <p><span className="font-medium">Notes:</span> {session.notes}</p>}
        {session.goals && session.goals.length > 0 && (
          <div>
            <p className="font-medium">Goals:</p>
            <ul className="list-disc list-inside text-sm text-gray-700">
              {session.goals.map((g, i) => (<li key={i}>{g}</li>))}
            </ul>
          </div>
        )}
        {session.metrics && (
          <div>
            <p className="font-medium">Metrics:</p>
            <ul className="text-sm text-gray-700">
              {Object.entries(session.metrics).map(([k, v]) => (<li key={k}>{k}: {v}</li>))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
