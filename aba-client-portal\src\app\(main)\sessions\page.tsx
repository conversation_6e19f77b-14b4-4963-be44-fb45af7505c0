"use client";

import { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { clientFetch } from '@/lib/clientFetch';
import type { SessionOut, ClientOut } from '@/lib/types';
import SessionList from '@/components/SessionList';

export default function SessionsPage() {
  const { data: sessions } = useQuery<SessionOut[]>({ queryKey: ['sessions'], queryFn: () => clientFetch<SessionOut[]>('/api/sessions') });
  const { data: clients } = useQuery<ClientOut[]>({ queryKey: ['clients'], queryFn: () => clientFetch<ClientOut[]>('/api/clients') });

  const [clientFilter, setClientFilter] = useState('');

  const filtered = useMemo(() => {
    if (!sessions) return [] as SessionOut[];
    if (!clientFilter) return sessions;
    return sessions.filter((s) => s.client_id === clientFilter);
  }, [sessions, clientFilter]);

  return (
    <div className="space-y-4">
      <h1 className="text-xl font-semibold">Sessions</h1>

      <div className="flex items-center gap-2">
        <label className="text-sm">Client</label>
        <select value={clientFilter} onChange={(e) => setClientFilter(e.target.value)} className="rounded border px-2 py-1">
          <option value="">All</option>
          {clients?.map((c) => (
            <option key={c._id} value={c._id}>{c.name}</option>
          ))}
        </select>
      </div>

      <SessionList sessions={filtered} />
    </div>
  );
}
