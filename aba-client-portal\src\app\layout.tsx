import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import QueryProvider from '@/lib/queryClient'
import Header from '@/components/Header'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
  icons: {
    icon: [
      {
        url: "/favicon.svg",
        type: "image/svg+xml",
      },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <QueryProvider>
          <Header />
          <main className="mx-auto max-w-6xl px-4 py-6">
            {children}
          </main>
        </QueryProvider>
      </body>
    </html>
  );
}

