import { NextRequest, NextResponse } from 'next/server';
import { ACCESS_TOKEN_COOKIE } from './auth';

export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';

export async function forwardRequestToBackend(req: NextRequest, backendPath: string) {
  const token = req.cookies.get(ACCESS_TOKEN_COOKIE)?.value;

  const search = req.nextUrl.search || '';
  const url = `${API_BASE_URL}${backendPath}${search}`;

  const incomingHeaders = new Headers(req.headers);
  incomingHeaders.delete('host');
  incomingHeaders.delete('cookie');

  if (token) {
    incomingHeaders.set('authorization', `Bearer ${token}`);
  }

  const method = req.method;
  let body: BodyInit | undefined = undefined;
  if (method !== 'GET' && method !== 'HEAD') {
    body = await req.text();
  }

  const backendResponse = await fetch(url, {
    method,
    headers: incomingHeaders,
    body,
    redirect: 'manual',
  });

  const res = new NextResponse(backendResponse.body, {
    status: backendResponse.status,
    headers: backendResponse.headers,
  });

  if (backendResponse.status === 401) {
    res.cookies.set(ACCESS_TOKEN_COOKIE, '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 0,
      expires: new Date(0),
    });
  }

  res.headers.delete('set-cookie');

  return res;
}
