import ClientCard from './ClientCard';
import type { ClientOut } from '@/lib/types';

export default function ClientList({ clients }: { clients: ClientOut[] }) {
  if (!clients.length) return <p className="text-sm text-gray-600">No clients</p>;
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {clients.map((c) => (
        <ClientCard key={c._id} client={c} />
      ))}
    </div>
  );
}
