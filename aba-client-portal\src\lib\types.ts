export interface TokenResponse { access_token: string; token_type: 'bearer'; }
export interface UserMe { _id: string; email: string; full_name?: string | null; role: 'parent' | 'therapist'; client_ids?: string[] | null; }
export interface ClientOut { _id: string; name: string; dob?: string | null; therapist_id?: string | null; parent_ids?: string[] | null; }
export interface SessionCreate { client_id: string; date?: string; notes?: string; goals?: string[]; metrics?: Record<string, number>; }
export interface SessionOut { _id: string; client_id: string; date: string; created_by: string; notes?: string | null; goals?: string[] | null; metrics?: Record<string, number> | null; }
export interface ProgressPoint { date: string; metrics: Record<string, number>; }
export interface ProgressSeriesResponse { client_id: string; points: ProgressPoint[]; }
export interface ProgressChartResponse { client_id: string; dates: string[]; series: Record<string, number[]>; }

export type Role = UserMe['role'];
