export async function clientFetch<T>(input: string, init?: RequestInit): Promise<T> {
  const res = await fetch(input, {
    ...init,
    headers: {
      'content-type': 'application/json',
      ...(init?.headers || {}),
    },
    credentials: 'same-origin',
  });

  if (res.status === 401) {
    if (typeof window !== 'undefined') {
      // Don't redirect if we're already on the login page to prevent infinite loops
      const currentPath = window.location.pathname;
      if (!currentPath.includes('/login')) {
        const params = new URLSearchParams({ message: 'Session expired. Please log in again.' });
        window.location.href = `/login?${params.toString()}`;
      }
    }
    throw new Error('Unauthorized');
  }

  if (!res.ok) {
    const text = await res.text();
    throw new Error(text || 'Request failed');
  }

  if (res.status === 204) {
    return undefined as unknown as T;
  }

  return (await res.json()) as T;
}
