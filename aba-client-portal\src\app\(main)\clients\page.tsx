"use client";

import { useQuery } from '@tanstack/react-query';
import { clientFetch } from '@/lib/clientFetch';
import type { ClientOut } from '@/lib/types';
import ClientList from '@/components/ClientList';

export default function ClientsPage() {
  const { data, isLoading } = useQuery<ClientOut[]>({ queryKey: ['clients'], queryFn: () => clientFetch<ClientOut[]>('/api/clients') });

  return (
    <div>
      <h1 className="text-xl font-semibold mb-4">Clients</h1>
      {isLoading ? <p>Loading…</p> : data ? <ClientList clients={data} /> : <p>No data</p>}
    </div>
  );
}
