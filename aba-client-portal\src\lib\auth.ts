import { NextResponse } from 'next/server';

export const ACCESS_TOKEN_COOKIE = 'access_token';

const isProd = process.env.NODE_ENV === 'production';

export function setAuthCookieOnResponse(res: NextResponse, token: string, maxAgeSeconds = 55 * 60) {
  res.cookies.set(ACCESS_TOKEN_COOKIE, token, {
    httpOnly: true,
    secure: isProd,
    sameSite: 'lax',
    path: '/',
    maxAge: maxAgeSeconds,
  });
}

export function clearAuthCookieOnResponse(res: NextResponse) {
  res.cookies.set(ACCESS_TOKEN_COOKIE, '', {
    httpOnly: true,
    secure: isProd,
    sameSite: 'lax',
    path: '/',
    maxAge: 0,
    expires: new Date(0),
  });
}
