"use client";

import { useQuery } from '@tanstack/react-query';
import { clientFetch } from '@/lib/clientFetch';
import type { ClientOut, ProgressChartResponse, SessionOut } from '@/lib/types';
import ProgressChart from '@/components/ProgressChart';
import { useState, useMemo } from 'react';
import SessionList from '@/components/SessionList';
import SessionForm from '@/components/SessionForm';
import RoleGuard from '@/components/RoleGuard';

export default function ClientDetailClient({ clientId }: { clientId: string }) {
  const [tab, setTab] = useState<'overview' | 'progress' | 'sessions'>('overview');
  const { data: client } = useQuery<ClientOut>({ queryKey: ['client', clientId], queryFn: () => clientFetch<ClientOut>(`/api/clients/${clientId}`) });
  const { data: chart } = useQuery<ProgressChartResponse>({ queryKey: ['client-progress-chart', clientId], queryFn: () => clientFetch<ProgressChartResponse>(`/api/analytics/clients/${clientId}/progress-chart`) });
  const { data: sessions } = useQuery<SessionOut[]>({ queryKey: ['sessions'], queryFn: () => clientFetch<SessionOut[]>('/api/sessions') });

  const sessionsForClient = useMemo(() => (sessions || []).filter((s) => s.client_id === clientId), [sessions, clientId]);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <h1 className="text-xl font-semibold flex-1">{client ? client.name : 'Client'}</h1>
        <nav className="flex gap-2 text-sm">
          {(['overview', 'progress', 'sessions'] as const).map((t) => (
            <button
              key={t}
              className={`px-3 py-1 rounded ${tab === t ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700'}`}
              onClick={() => setTab(t)}
            >
              {t[0].toUpperCase() + t.slice(1)}
            </button>
          ))}
        </nav>
      </div>

      {tab === 'overview' && client && (
        <div className="space-y-2">
          <p><span className="font-medium">Name:</span> {client.name}</p>
          {client.dob && <p><span className="font-medium">DOB:</span> {client.dob}</p>}
          {client.parent_ids && client.parent_ids.length > 0 && (
            <p className="text-sm text-gray-600">Parents: {client.parent_ids.length}</p>
          )}
        </div>
      )}

      {tab === 'progress' && (
        <div className="space-y-4">
          {chart ? <ProgressChart data={chart} /> : <p>Loading chart…</p>}
        </div>
      )}

      {tab === 'sessions' && (
        <div className="space-y-4">
          <RoleGuard allow={['therapist']}>
            <div className="rounded border p-4">
              <h3 className="font-semibold mb-2">New Session</h3>
              <SessionForm defaultClientId={clientId} />
            </div>
          </RoleGuard>
          <SessionList sessions={sessionsForClient} />
        </div>
      )}
    </div>
  );
}
