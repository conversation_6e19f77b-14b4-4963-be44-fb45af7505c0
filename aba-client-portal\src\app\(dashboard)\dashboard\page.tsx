"use client";

import { useQuery } from '@tanstack/react-query';
import { clientFetch } from '@/lib/clientFetch';
import type { UserMe, ClientOut, SessionOut } from '@/lib/types';
import ClientList from '@/components/ClientList';
import SessionList from '@/components/SessionList';
import Link from 'next/link';

export default function DashboardPage() {
  const { data: me, error: meError } = useQuery<UserMe>({
    queryKey: ['me'],
    queryFn: () => clientFetch<UserMe>('/api/auth/me'),
    retry: false, // Don't retry auth queries
  });
  const { data: clients } = useQuery<ClientOut[]>({ queryKey: ['clients'], queryFn: () => clientFetch<ClientOut[]>('/api/clients'), enabled: !!me });
  const { data: sessions } = useQuery<SessionOut[]>({ queryKey: ['sessions'], queryFn: () => clientFetch<SessionOut[]>('/api/sessions'), enabled: !!me });

  if (meError || !me) return null;

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-lg font-semibold">Welcome{me.full_name ? `, ${me.full_name}` : ''}.</h2>
        <p className="text-sm text-gray-600">Role: {me.role}</p>
      </div>

      <section className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold">Your Clients</h3>
          <Link href="/clients" className="text-sm text-blue-600">View all</Link>
        </div>
        {clients ? <ClientList clients={clients} /> : <p>Loading…</p>}
      </section>

      <section className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold">Recent Sessions</h3>
          <Link href="/sessions" className="text-sm text-blue-600">View all</Link>
        </div>
        {sessions ? <SessionList sessions={sessions.slice(0, 5)} /> : <p>Loading…</p>}
      </section>
    </div>
  );
}
