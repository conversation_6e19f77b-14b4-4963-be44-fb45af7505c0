"use client";

import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { sessionCreateSchema, type SessionCreateSchema } from '@/lib/validators';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { clientFetch } from '@/lib/clientFetch';
import type { ClientOut, SessionOut } from '@/lib/types';
import { useState } from 'react';

export default function SessionForm({ defaultClientId }: { defaultClientId?: string }) {
  const queryClient = useQueryClient();
  const { data: clients } = useQuery<ClientOut[]>({ queryKey: ['clients'], queryFn: () => clientFetch<ClientOut[]>('/api/clients') });
  const [serverError, setServerError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
  } = useForm<SessionCreateSchema>({
    resolver: zod<PERSON>esolver(sessionCreateSchema),
    defaultValues: { client_id: defaultClientId || '' },
  });

  const { fields, append, remove } = useFieldArray({ control, name: 'goals' as never });

  const metricsState = useState<{ id: string; key: string; value: string }[]>([]);

  const mutation = useMutation({
    mutationFn: (payload: SessionCreateSchema) => clientFetch<SessionOut>('/api/sessions', { method: 'POST', body: JSON.stringify(preparePayload(payload)) }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      queryClient.invalidateQueries({ queryKey: ['sessions-by-client'] });
      queryClient.invalidateQueries({ queryKey: ['client-progress'] });
    },
  });

  function preparePayload(values: SessionCreateSchema): SessionCreateSchema {
    const metricsArray = metricsState[0];
    const metrics: Record<string, number> = {};
    for (const m of metricsArray) {
      if (m.key && m.value !== '') metrics[m.key] = Number(m.value);
    }
    return { ...values, metrics: Object.keys(metrics).length ? metrics : undefined };
  }

  const onSubmit = async (values: SessionCreateSchema) => {
    setServerError(null);
    try {
      await mutation.mutateAsync(values);
    } catch (e) {
      setServerError((e as Error)?.message || 'Failed to create session');
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {serverError && <p className="text-sm text-red-700 bg-red-50 p-2 rounded" role="alert">{serverError}</p>}

      <div>
        <label className="block text-sm font-medium">Client</label>
        <select
          {...register('client_id')}
          className="mt-1 w-full rounded border border-gray-300 px-3 py-2"
        >
          <option value="">Select a client</option>
          {clients?.map((c) => (
            <option key={c._id} value={c._id}>{c.name}</option>
          ))}
        </select>
        {errors.client_id && <p className="text-xs text-red-600 mt-1">{errors.client_id.message}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium">Date</label>
        <input type="datetime-local" {...register('date')} className="mt-1 w-full rounded border border-gray-300 px-3 py-2" />
      </div>

      <div>
        <label className="block text-sm font-medium">Notes</label>
        <textarea {...register('notes')} className="mt-1 w-full rounded border border-gray-300 px-3 py-2" rows={3} />
      </div>

      <div>
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium">Goals</label>
          <button type="button" onClick={() => append('' as unknown as string)} className="text-sm text-blue-600">Add</button>
        </div>
        <div className="space-y-2 mt-1">
          {fields.map((field, idx) => (
            <div key={field.id} className="flex items-center gap-2">
              <input className="flex-1 rounded border border-gray-300 px-3 py-2" {...register(`goals.${idx}` as const)} />
              <button type="button" onClick={() => remove(idx)} className="text-sm text-gray-600">Remove</button>
            </div>
          ))}
        </div>
      </div>

      <div>
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium">Metrics</label>
          <button type="button" onClick={() => metricsState[1]([...metricsState[0], { id: crypto.randomUUID(), key: '', value: '' }])} className="text-sm text-blue-600">Add</button>
        </div>
        <div className="space-y-2 mt-1">
          {metricsState[0].map((m, idx) => (
            <div key={m.id} className="grid grid-cols-5 gap-2">
              <input
                placeholder="Metric name"
                value={m.key}
                onChange={(e) => {
                  const arr = [...metricsState[0]];
                  arr[idx] = { ...arr[idx], key: e.target.value };
                  metricsState[1](arr);
                }}
                className="col-span-2 rounded border border-gray-300 px-3 py-2"
              />
              <input
                placeholder="Value"
                type="number"
                value={m.value}
                onChange={(e) => {
                  const arr = [...metricsState[0]];
                  arr[idx] = { ...arr[idx], value: e.target.value };
                  metricsState[1](arr);
                }}
                className="col-span-2 rounded border border-gray-300 px-3 py-2"
              />
              <button type="button" onClick={() => metricsState[1](metricsState[0].filter((_, i) => i !== idx))} className="text-sm text-gray-600">Remove</button>
            </div>
          ))}
        </div>
      </div>

      <button type="submit" disabled={isSubmitting} className="rounded bg-green-600 text-white py-2 px-4 hover:bg-green-700 disabled:opacity-50">
        {isSubmitting ? 'Saving…' : 'Create Session'}
      </button>
    </form>
  );
}
