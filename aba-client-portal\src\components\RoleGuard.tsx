"use client";

import { useQuery } from '@tanstack/react-query';
import { clientFetch } from '@/lib/clientFetch';
import type { Role, UserMe } from '@/lib/types';

export default function RoleGuard({ allow, children }: { allow: Role[]; children: React.ReactNode }) {
  const { data: me, isLoading, error } = useQuery<UserMe>({
    queryKey: ['me'],
    queryFn: () => clientFetch<UserMe>('/api/auth/me'),
    retry: false, // Don't retry auth queries
  });

  if (isLoading) return null;
  if (error || !me || !allow.includes(me.role)) return null;

  return <>{children}</>;
}
