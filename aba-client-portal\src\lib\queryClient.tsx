"use client";

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import React from 'react';

let browserQueryClient: QueryClient | null = null;

function getQueryClient() {
  if (!browserQueryClient) {
    browserQueryClient = new QueryClient({
      defaultOptions: {
        queries: {
          refetchOnWindowFocus: false, // Disable refetch on window focus to prevent auth loops
          retry: (failureCount, error) => {
            // Don't retry auth-related errors (401/403)
            if (error instanceof Error && (error.message.includes('Unauthorized') || error.message.includes('401'))) {
              return false;
            }
            // Retry other errors up to 1 time
            return failureCount < 1;
          },
        },
      },
    });
  }
  return browserQueryClient;
}

export default function QueryProvider({ children }: { children: React.ReactNode }) {
  const client = getQueryClient();
  return (
    <QueryClientProvider client={client}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
