import Link from 'next/link';
import type { ClientOut } from '@/lib/types';

export default function ClientCard({ client }: { client: ClientOut }) {
  return (
    <div className="border rounded p-4 hover:shadow-sm transition">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-gray-900">{client.name}</h3>
        <Link href={`/clients/${client._id}`} className="text-blue-600 hover:underline text-sm">View</Link>
      </div>
      {client.dob && <p className="text-sm text-gray-600 mt-1">DOB: {client.dob}</p>}
    </div>
  );
}
