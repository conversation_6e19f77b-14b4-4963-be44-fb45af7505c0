import Link from 'next/link';
import type { SessionOut } from '@/lib/types';

export default function SessionList({ sessions }: { sessions: SessionOut[] }) {
  if (!sessions.length) return <p className="text-sm text-gray-600">No sessions</p>;
  return (
    <ul className="divide-y border rounded">
      {sessions.map((s) => (
        <li key={s._id} className="p-3 flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-900">{new Date(s.date).toLocaleString()}</p>
            {s.notes && <p className="text-xs text-gray-600 mt-0.5 line-clamp-1">{s.notes}</p>}
          </div>
          <Link href={`/sessions/${s._id}`} className="text-blue-600 text-sm hover:underline">Open</Link>
        </li>
      ))}
    </ul>
  );
}
